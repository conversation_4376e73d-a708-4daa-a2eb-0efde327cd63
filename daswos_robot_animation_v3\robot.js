// Daswos Robot Animation using p5.js with advanced image-based animation
// Based on detailed image analysis and animation feature requirements

// Image variables for different robot views
let robotImages = {
  front: null,
  side: null,
  threeQuarter: null,
  back: null,
  top: null
};

// Component-specific images (for potential future use)
let robotParts = {
  head: null,
  body: null,
  arms: {
    left: null,
    right: null
  },
  legs: null
};

// Animation state variables
let robotState = 'idle'; // Current animation state
let previousState = 'idle'; // Previous animation state for transitions
let stateStartTime = 0; // When the current state started
let transitionProgress = 0; // Progress of transition between states (0-1)
let isTransitioning = false; // Whether currently in transition between states

// Position and movement variables
let robotX; // Current X position of robot
let robotY; // Current Y position of robot
let targetX = 0; // Target X position for rolling animation
let targetY = 0; // Target Y position for rolling animation
let isRolling = false; // Whether the robot is currently rolling
let rollDirection = 0; // Direction of rolling in radians
let rollSpeed = 0; // Speed of rolling

// Animation effect variables
let headRotation = 0; // Rotation of the head relative to body
let headBobAmount = 0; // Amount of head bobbing
let bodyRotation = 0; // Rotation of the body/wheel
let bodyRotationSpeed = 0; // Speed of body rotation
let armLeftRotation = 0; // Rotation of left arm
let armRightRotation = 0; // Rotation of right arm
let legsVisible = true; // Whether legs are currently visible
let legsVisibility = 1; // Opacity of legs (0-1)
let eyeBlinkTime = 0; // Time for eye blinking effect
let isBlinking = false; // Whether eyes are currently blinking
let talkPulse = 0; // Pulsing effect for talking animation
let dancePhase = 0; // Phase of the dance animation
let searchAngle = 0; // Angle for search animation

// View management variables
let currentView = 'front'; // Current view being displayed
let targetView = 'front'; // Target view to transition to
let viewTransitionProgress = 0; // Progress of view transition (0-1)

// Mouse interaction variables
let lastMouseX = 0; // Last mouse X position for interaction
let lastMouseY = 0; // Last mouse Y position for interaction
let mouseInteractionTimer = 0; // Timer for mouse interaction effects

// Constants
const TRANSITION_DURATION = 500; // Duration of state transitions in ms
const VIEW_TRANSITION_DURATION = 300; // Duration of view transitions in ms
const SHADOW_OPACITY = 0.3; // Opacity of the shadow
const SHADOW_SCALE_Y = 0.2; // Y-scale of the shadow ellipse
const SHADOW_OFFSET_Y = 20; // Y-offset of the shadow from the robot center

// Scale-related variables
let robotScale = 0.5; // Current scale factor for robot images
let targetScale = 0.5; // Target scale for smooth transitions
const MIN_SCALE = 0.2; // Minimum allowed scale
const MAX_SCALE = 1.5; // Maximum allowed scale
const DEFAULT_SCALE = 0.5; // Default scale value
const SCALE_TRANSITION_SPEED = 0.1; // Speed of scale transitions

// Centering and position variables
let centerX = 0; // Center X position of the screen
let centerY = 0; // Center Y position of the screen
let shouldReturnToCenter = false; // Whether robot should return to center
const POSITION_TRANSITION_SPEED = 0.05; // Speed of position transitions
let danceStartX = 0; // Starting X position for dance (to oscillate around)

// Setup function - runs once at the beginning
function setup() {
  // Create canvas that fills the window
  createCanvas(windowWidth, windowHeight);

  // Initialize robot position to center of screen
  centerX = width / 2;
  centerY = height / 2;
  robotX = centerX;
  robotY = centerY;

  // Set up button event listeners
  document.getElementById('idleBtn').addEventListener('click', () => setRobotState('idle'));
  document.getElementById('talkBtn').addEventListener('click', () => setRobotState('talk'));
  document.getElementById('danceBtn').addEventListener('click', () => setRobotState('dance'));
  document.getElementById('rollBtn').addEventListener('click', () => {
    // Roll to a random position on screen
    targetX = random(width * 0.2, width * 0.8);
    targetY = random(height * 0.2, height * 0.8);
    setRobotState('roll');
  });
  document.getElementById('searchBtn').addEventListener('click', () => setRobotState('search'));
  document.getElementById('centerBtn').addEventListener('click', () => centerRobot());

  // Set up scale control event listeners
  document.getElementById('scaleSlider').addEventListener('input', (e) => {
    setRobotScale(parseFloat(e.target.value));
  });
  document.getElementById('scaleUpBtn').addEventListener('click', () => {
    setRobotScale(Math.min(MAX_SCALE, targetScale + 0.1));
  });
  document.getElementById('scaleDownBtn').addEventListener('click', () => {
    setRobotScale(Math.max(MIN_SCALE, targetScale - 0.1));
  });
  document.getElementById('resetScaleBtn').addEventListener('click', () => {
    setRobotScale(DEFAULT_SCALE);
  });

  // Hide loading message once setup is complete
  document.getElementById('loading').style.display = 'none';

  // Initial entrance animation
  robotX = -100; // Start off-screen
  targetX = centerX;
  targetY = centerY;
  setRobotState('roll');
}

// Preload function - loads assets before setup
function preload() {
  // Load all robot view images
  robotImages.front = loadImage('images/robot_front_view.png');
  robotImages.side = loadImage('images/robot_side_view.png');
  robotImages.threeQuarter = loadImage('images/robot_three_quarter_view.png');
  robotImages.back = loadImage('images/robot_back_view.png');
  robotImages.top = loadImage('images/robot_top_view.png');
}

// Draw function - runs every frame
function draw() {
  // Clear background
  background('#f0f0f0');

  // Update animation based on current state
  updateAnimation();

  // Draw robot
  drawRobot();

  // Handle mouse interaction
  handleMouseInteraction();
}

// Update animation based on current state
function updateAnimation() {
  // Calculate time in current state
  let currentTime = millis();
  let timeInState = currentTime - stateStartTime;

  // Handle smooth scale transitions
  if (abs(robotScale - targetScale) > 0.01) {
    robotScale = lerp(robotScale, targetScale, SCALE_TRANSITION_SPEED);
  } else {
    robotScale = targetScale;
  }

  // Handle smooth return to center when needed
  if (shouldReturnToCenter && !isRolling) {
    let distanceToCenter = dist(robotX, robotY, centerX, centerY);
    if (distanceToCenter > 2) {
      robotX = lerp(robotX, centerX, POSITION_TRANSITION_SPEED);
      robotY = lerp(robotY, centerY, POSITION_TRANSITION_SPEED);
    } else {
      robotX = centerX;
      robotY = centerY;
      shouldReturnToCenter = false;
    }
  }

  // Handle rolling movement if needed
  if (isRolling) {
    // Calculate direction to target
    let dx = targetX - robotX;
    let dy = targetY - robotY;
    let distance = sqrt(dx*dx + dy*dy);

    // If we're close enough to target, stop rolling
    if (distance < 5) {
      isRolling = false;
      if (robotState === 'roll') {
        setRobotState('idle');
      }
    } else {
      // Update position and rotation
      rollDirection = atan2(dy, dx);
      rollSpeed = min(distance * 0.05, 5);
      robotX += cos(rollDirection) * rollSpeed;
      robotY += sin(rollDirection) * rollSpeed;

      // Rotate body based on movement (wheel rotation)
      bodyRotationSpeed = rollSpeed * 0.2;
      bodyRotation += bodyRotationSpeed;

      // Set appropriate view based on roll direction
      if (abs(cos(rollDirection)) > abs(sin(rollDirection))) {
        // Moving more horizontally
        targetView = cos(rollDirection) > 0 ? 'side' : 'side';
      } else {
        // Moving more vertically
        targetView = sin(rollDirection) > 0 ? 'threeQuarter' : 'back';
      }

      // Hide legs during rolling
      legsVisible = false;
      legsVisibility = max(0, legsVisibility - 0.1);
    }
  } else if (robotState !== 'roll' && !legsVisible) {
    // Show legs when not rolling
    legsVisible = true;
    legsVisibility = min(1, legsVisibility + 0.05);
  }

  // Handle view transitions
  if (currentView !== targetView) {
    viewTransitionProgress = min(1, timeInState / VIEW_TRANSITION_DURATION);
    if (viewTransitionProgress >= 1) {
      currentView = targetView;
      viewTransitionProgress = 0;
    }
  }

  // State-specific updates
  switch (robotState) {
    case 'idle':
      // Subtle bobbing motion
      headBobAmount = sin(currentTime * 0.002) * 5;

      // Occasional head rotation
      headRotation = sin(currentTime * 0.001) * 0.1;

      // Subtle arm movements
      armLeftRotation = sin(currentTime * 0.001) * 0.05;
      armRightRotation = sin(currentTime * 0.001 + PI) * 0.05;

      // Occasional eye blink
      if (currentTime > eyeBlinkTime && !isBlinking) {
        isBlinking = true;
        eyeBlinkTime = currentTime + 200; // Blink duration
      } else if (currentTime > eyeBlinkTime && isBlinking) {
        isBlinking = false;
        eyeBlinkTime = currentTime + random(2000, 5000); // Time until next blink
      }

      // Default to front view in idle
      if (!isRolling) targetView = 'front';

      // Ensure legs are visible
      legsVisible = true;
      legsVisibility = min(1, legsVisibility + 0.05);
      break;

    case 'talk':
      // Pulsing head effect
      talkPulse = sin(currentTime * 0.01) * 0.05;

      // Head bobbing synchronized with "speech"
      headBobAmount = sin(currentTime * 0.01) * 3;

      // Arm gestures synchronized with talking
      armLeftRotation = sin(currentTime * 0.008) * 0.2;
      armRightRotation = sin(currentTime * 0.008 + PI) * 0.2;

      // Always use front view when talking
      targetView = 'front';

      // Ensure legs are visible
      legsVisible = true;
      legsVisibility = min(1, legsVisibility + 0.05);
      break;

    case 'dance':
      // Update dance phase
      dancePhase += 0.05;

      // Head bobbing with dance rhythm
      headBobAmount = sin(dancePhase * 2) * 8;
      headRotation = sin(dancePhase) * 0.2;

      // Arm movements with dance rhythm
      armLeftRotation = sin(dancePhase) * 0.4;
      armRightRotation = sin(dancePhase + PI) * 0.4;

      // Side-to-side body movement (oscillate around center)
      if (!isRolling) {
        robotX = danceStartX + sin(dancePhase) * 30;
      }

      // Cycle through views for dancing
      if (timeInState % 2000 < 500) {
        targetView = 'front';
      } else if (timeInState % 2000 < 1000) {
        targetView = 'threeQuarter';
      } else if (timeInState % 2000 < 1500) {
        targetView = 'side';
      } else {
        targetView = 'threeQuarter';
      }

      // Ensure legs are visible with extra bounce
      legsVisible = true;
      legsVisibility = min(1, legsVisibility + 0.05);
      break;

    case 'search':
      // Update search angle
      searchAngle += 0.03;

      // Head rotation for searching
      headRotation = sin(searchAngle) * 0.3;

      // Subtle body movement
      headBobAmount = sin(currentTime * 0.005) * 3;

      // Arm movements for searching/pointing
      armLeftRotation = sin(searchAngle * 0.5) * 0.2;
      armRightRotation = sin(searchAngle * 0.5 + PI) * 0.2;

      // Cycle through views for searching
      if (timeInState % 3000 < 1000) {
        targetView = 'front';
      } else if (timeInState % 3000 < 2000) {
        targetView = 'threeQuarter';
      } else {
        targetView = 'side';
      }

      // Ensure legs are visible
      legsVisible = true;
      legsVisibility = min(1, legsVisibility + 0.05);
      break;

    case 'roll':
      // Most updates handled in the rolling logic above

      // Add some head bobbing during roll
      headBobAmount = sin(currentTime * 0.01) * 3;

      // Slight arm movement based on speed
      armLeftRotation = sin(currentTime * 0.01) * 0.1 * (rollSpeed * 0.1);
      armRightRotation = sin(currentTime * 0.01 + PI) * 0.1 * (rollSpeed * 0.1);

      // Hide legs during rolling
      legsVisible = false;
      legsVisibility = max(0, legsVisibility - 0.1);
      break;
  }
}

// Draw the robot based on current state and view
function drawRobot() {
  push();
  translate(robotX, robotY);

  // Draw shadow
  drawShadow();

  // Apply bobbing effect
  translate(0, headBobAmount);

  // Scale the robot
  scale(robotScale);

  // Apply body rotation for wheel effect
  if (robotState === 'roll') {
    push();
    rotate(bodyRotation);
  }

  // Determine which image to draw based on current view and transition
  let currentImage = robotImages[currentView];

  // If transitioning between views, blend them
  if (currentView !== targetView && viewTransitionProgress > 0) {
    // Draw current view with fading opacity
    tint(255, 255, 255, 255 * (1 - viewTransitionProgress));
    image(currentImage, -currentImage.width/2, -currentImage.height/2);

    // Draw target view with increasing opacity
    tint(255, 255, 255, 255 * viewTransitionProgress);
    let targetImage = robotImages[targetView];
    image(targetImage, -targetImage.width/2, -targetImage.height/2);

    // Reset tint
    noTint();
  } else {
    // Apply effects based on state
    if (robotState === 'talk') {
      // Subtle pulsing for talking
      scale(1 + talkPulse);
    }

    // Draw the current view
    image(currentImage, -currentImage.width/2, -currentImage.height/2);
  }

  if (robotState === 'roll') {
    pop(); // End body rotation
  }

  pop();
}

// Draw shadow beneath the robot
function drawShadow() {
  push();
  translate(0, SHADOW_OFFSET_Y);
  fill(0, 0, 0, SHADOW_OPACITY * 255);
  noStroke();
  ellipse(0, 0, 120 * robotScale, 30 * robotScale * SHADOW_SCALE_Y);
  pop();
}

// Handle mouse interaction with the robot
function handleMouseInteraction() {
  // Calculate distance from mouse to robot
  let d = dist(mouseX, mouseY, robotX, robotY);

  // If mouse is near robot and moving (scale interaction range with robot size)
  if (d < 150 * robotScale && (abs(mouseX - lastMouseX) > 5 || abs(mouseY - lastMouseY) > 5)) {
    // Make robot look toward mouse
    let angle = atan2(mouseY - robotY, mouseX - robotX);
    headRotation = lerp(headRotation, angle * 0.2, 0.1);

    // Set appropriate view based on mouse position
    if (abs(cos(angle)) > 0.7) {
      // Mouse is more to the sides
      targetView = cos(angle) > 0 ? 'threeQuarter' : 'threeQuarter';
    } else {
      // Mouse is more above/below
      targetView = sin(angle) > 0 ? 'front' : 'top';
    }

    // Reset interaction timer
    mouseInteractionTimer = millis() + 1000;
  }

  // If interaction timer expired, return to default view for current state
  if (mouseInteractionTimer > 0 && millis() > mouseInteractionTimer) {
    mouseInteractionTimer = 0;
    // Return to default view for current state
    switch (robotState) {
      case 'idle':
        targetView = 'front';
        break;
      case 'talk':
        targetView = 'front';
        break;
      case 'dance':
        // Keep current view for dance
        break;
      case 'search':
        // Keep current view for search
        break;
      case 'roll':
        // Keep current view for roll
        break;
    }
  }

  // Store current mouse position for next frame
  lastMouseX = mouseX;
  lastMouseY = mouseY;
}

// Set the robot's animation state
function setRobotState(state) {
  // Store previous state for transitions
  previousState = robotState;
  robotState = state;

  // Record start time for this state
  stateStartTime = millis();

  // Reset state-specific variables
  switch (state) {
    case 'idle':
      if (!isRolling) {
        targetView = 'front';
        shouldReturnToCenter = true;
      }
      break;

    case 'talk':
      targetView = 'front';
      talkPulse = 0;
      shouldReturnToCenter = true;
      break;

    case 'dance':
      dancePhase = 0;
      // Set dance start position to current center
      danceStartX = centerX;
      shouldReturnToCenter = false; // Dance has its own movement
      break;

    case 'search':
      searchAngle = 0;
      shouldReturnToCenter = true;
      break;

    case 'roll':
      isRolling = true;
      shouldReturnToCenter = false; // Rolling has its own target
      // Hide legs immediately when starting to roll
      legsVisible = false;
      break;
  }
}

// Handle mouse clicks
function mousePressed() {
  // If clicking near the robot, make it do a special animation
  let d = dist(mouseX, mouseY, robotX, robotY);
  if (d < 100 * robotScale) {
    // Quick reaction animation
    headBobAmount = -10;
    setTimeout(() => { headBobAmount = 0; }, 300);
  } else {
    // Roll to where user clicked
    targetX = mouseX;
    targetY = mouseY;
    setRobotState('roll');
  }
}

// Handle window resizing
function windowResized() {
  resizeCanvas(windowWidth, windowHeight);

  // Update center position
  centerX = width / 2;
  centerY = height / 2;

  // If robot should be centered, update its position
  if (shouldReturnToCenter || robotState === 'idle' || robotState === 'talk' || robotState === 'search') {
    shouldReturnToCenter = true;
  }

  // Update dance start position if dancing
  if (robotState === 'dance') {
    danceStartX = centerX;
  }
}

// Set robot scale with bounds checking and UI updates
function setRobotScale(newScale) {
  // Clamp the scale to valid range
  targetScale = constrain(newScale, MIN_SCALE, MAX_SCALE);

  // Update UI elements
  updateScaleDisplay();
}

// Update the scale display elements
function updateScaleDisplay() {
  const scaleSlider = document.getElementById('scaleSlider');
  const scaleValue = document.getElementById('scaleValue');

  if (scaleSlider && scaleValue) {
    scaleSlider.value = targetScale;
    scaleValue.textContent = Math.round(targetScale * 100) + '%';
  }
}

// Center the robot smoothly
function centerRobot() {
  shouldReturnToCenter = true;

  // If dancing, update the dance start position to center
  if (robotState === 'dance') {
    danceStartX = centerX;
  }
}
